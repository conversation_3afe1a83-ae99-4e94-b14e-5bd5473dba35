2025-07-08 08:15:13 - __main__ - INFO - ==================================================
2025-07-08 08:15:13 - __main__ - INFO - 2925邮箱服务端启动 - Windows版本
2025-07-08 08:15:13 - __main__ - INFO - ==================================================
2025-07-08 08:15:13 - __main__ - INFO - 服务端口: 8080
2025-07-08 08:15:13 - __main__ - INFO - 邮箱前缀: codegmail
2025-07-08 08:15:13 - __main__ - INFO - 邮箱域名: 2925.com
2025-07-08 08:15:13 - __main__ - INFO - 日志目录: D:\PythonProject\服务验证端\logs
2025-07-08 08:15:13 - __main__ - INFO - 日志轮转: 10MB/文件, 保留5个备份
2025-07-08 08:15:13 - __main__ - INFO - 启动时间: 2025-07-08 08:15:13
2025-07-08 08:15:13 - __main__ - INFO - Python版本: 3.13.5 (tags/v3.13.5:6cb20a2, Jun 11 2025, 16:15:46) [MSC v.1943 64 bit (AMD64)]
2025-07-08 08:15:13 - __main__ - INFO - 操作系统: Windows
2025-07-08 08:15:13 - __main__ - INFO - ==================================================
2025-07-08 08:15:13 - __main__ - INFO - 正在测试2925邮箱系统连接...
2025-07-08 08:15:13 - __main__ - INFO - 正在登录邮箱前缀: codegmail
2025-07-08 08:15:13 - __main__ - INFO - 正在登录邮箱前缀: codegmail
2025-07-08 08:15:16 - __main__ - INFO - 登录成功: codegmail
2025-07-08 08:15:16 - __main__ - INFO - 登录成功: codegmail
2025-07-08 08:15:16 - __main__ - INFO - ✅ 2925邮箱系统登录成功
2025-07-08 08:15:16 - __main__ - INFO - ✅ 邮箱前缀 'codegmail' 连接正常
2025-07-08 08:15:16 - __main__ - WARNING - ⚠️ 获取验证码失败: 未指定目标邮箱
2025-07-08 08:15:16 - __main__ - INFO - ==================================================
2025-07-08 08:15:16 - __main__ - WARNING - ⚠️  服务启动完成，但2925邮箱系统连接异常
2025-07-08 08:15:16 - __main__ - WARNING - ⚠️  API调用时会自动重试连接
2025-07-08 08:15:16 - __main__ - INFO - 🌐 服务地址: http://0.0.0.0:8080
2025-07-08 08:15:16 - __main__ - INFO - 📋 可用接口: /, /health, /get_email, /get_code, /get_domains
2025-07-08 08:15:16 - __main__ - INFO - ==================================================
2025-07-08 08:15:16 - __main__ - WARNING - 未安装waitress，使用Flask开发服务器运行
2025-07-08 08:15:16 - __main__ - WARNING - 建议安装waitress: pip install waitress
2025-07-08 08:15:16 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:8080
 * Running on http://*************:8080
2025-07-08 08:15:16 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-08 08:15:36 - __main__ - INFO - � API调用 - 单次获取验证码: <EMAIL>
2025-07-08 08:15:36 - __main__ - INFO - 单次获取邮箱验证码: <EMAIL>
2025-07-08 08:15:36 - __main__ - INFO - 创建新的登录实例: codegmail
2025-07-08 08:15:36 - __main__ - INFO - 正在连接2925邮箱系统 (前缀: codegmail)...
2025-07-08 08:15:36 - __main__ - INFO - 正在登录邮箱前缀: codegmail
2025-07-08 08:15:40 - __main__ - INFO - 登录成功: codegmail
2025-07-08 08:15:40 - __main__ - INFO - ✅ 2925邮箱登录成功: <EMAIL>
2025-07-08 08:15:41 - __main__ - INFO - ================================================================================
2025-07-08 08:15:41 - __main__ - INFO - 📧 完整邮件内容 (调试模式)
2025-07-08 08:15:41 - __main__ - INFO - ================================================================================
2025-07-08 08:15:41 - __main__ - INFO - 发件人: Unknown
2025-07-08 08:15:41 - __main__ - INFO - 收件人: Unknown
2025-07-08 08:15:41 - __main__ - INFO - 主题: No Subject
2025-07-08 08:15:41 - __main__ - INFO - 时间: Unknown
2025-07-08 08:15:41 - __main__ - INFO - 邮件ID: 0ca0fa9edea74bc498439e329b338227
2025-07-08 08:15:41 - __main__ - INFO - --------------------------------------------------------------------------------
2025-07-08 08:15:41 - __main__ - INFO - 邮件正文 (bodyText):
2025-07-08 08:15:41 - __main__ - INFO - 



  

  

  

    

      

      

        

          

        



        

        



          

          



            Your verification code is: 811700



          



        



        If you are having any issues with your account, please don't hesitate to contact us by replying to this mail.



        

        Thanks!

        



        Augment Code



        

        

        

          If you did not make this request, you can safely ignore this email. Never share this one-time code with anyone - Augment support will never ask for your verification code. Your account remains secure and no action is needed.

        

      

      

    

  






2025-07-08 08:15:41 - __main__ - INFO - --------------------------------------------------------------------------------
2025-07-08 08:15:41 - __main__ - INFO - 邮件正文 (bodyHtml):
2025-07-08 08:15:41 - __main__ - INFO - No HTML Body
2025-07-08 08:15:41 - __main__ - INFO - --------------------------------------------------------------------------------
2025-07-08 08:15:41 - __main__ - INFO - 原始邮件数据:
2025-07-08 08:15:41 - __main__ - INFO - {
  "mailId": "0ca0fa9edea74bc498439e329b338227",
  "mailSubject": "Welcome to Augment Code",
  "mailFrom": {
    "displayName": "Augment Code",
    "emailAddress": "<EMAIL>"
  },
  "mailTo": [
    {
      "displayName": null,
      "emailAddress": "<EMAIL>"
    }
  ],
  "mailCc": null,
  "mailBcc": null,
  "bodyHtmlText": "<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Transitional//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd\">\r\n<html xmlns=\"http://www.w3.org/1999/xhtml\">\r\n  <head>\r\n    <meta http-equiv=\"Content-Type\" content=\"text/html; charset=UTF-8\">\r\n    <style type=\"text/css\">.ExternalClass,.ExternalClass div,.ExternalClass font,.ExternalClass p,.ExternalClass span,.ExternalClass td,img{line-height:100%}#outlook a{padding:0}.ExternalClass,.ReadMsgBody{width:100%}a,blockquote,body,li,p,table,td{-webkit-text-size-adjust:100%;-ms-text-size-adjust:100%}table,td{mso-table-lspace:0;mso-table-rspace:0}img{-ms-interpolation-mode:bicubic;border:0;height:auto;outline:0;text-decoration:none}table{border-collapse:collapse!important}#bodyCell,#bodyTable,body{height:100%!important;margin:0;padding:0;font-family:ProximaNova,sans-serif}#bodyCell{padding:20px}#bodyTable{width:600px}@font-face{font-family:ProximaNova;src:url(https://cdn.auth0.com/fonts/proxima-nova/proximanova-regular-webfont-webfont.eot);src:url(https://cdn.auth0.com/fonts/proxima-nova/proximanova-regular-webfont-webfont.eot?#iefix) format('embedded-opentype'),url(https://cdn.auth0.com/fonts/proxima-nova/proximanova-regular-webfont-webfont.woff) format('woff');font-weight:400;font-style:normal}@font-face{font-family:ProximaNova;src:url(https://cdn.auth0.com/fonts/proxima-nova/proximanova-semibold-webfont-webfont.eot);src:url(https://cdn.auth0.com/fonts/proxima-nova/proximanova-semibold-webfont-webfont.eot?#iefix) format('embedded-opentype'),url(https://cdn.auth0.com/fonts/proxima-nova/proximanova-semibold-webfont-webfont.woff) format('woff');font-weight:600;font-style:normal}@media only screen and (max-width:480px){#bodyTable,body{width:100%!important}a,blockquote,body,li,p,table,td{-webkit-text-size-adjust:none!important}body{min-width:100%!important}#bodyTable{max-width:600px!important}#signIn{max-width:280px!important}}\r\n</style>\r\n  </head>\r\n  <body leftmargin=\"0\" marginwidth=\"0\" topmargin=\"0\" marginheight=\"0\" offset=\"0\" style=\"-webkit-text-size-adjust: 100%;-ms-text-size-adjust: 100%;margin: 0;padding: 0;font-family: &quot;ProximaNova&quot;, sans-serif;height: 100% !important;\"><center>\r\n  <table style=\"width: 600px;-webkit-text-size-adjust: 100%;-ms-text-size-adjust: 100%;mso-table-lspace: 0pt;mso-table-rspace: 0pt;margin: 0;padding: 0;font-family: &quot;ProximaNova&quot;, sans-serif;border-collapse: collapse !important;height: 100% !important;\" align=\"center\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" height=\"100%\" width=\"100%\" id=\"bodyTable\">\r\n    <tr>\r\n      <td align=\"center\" valign=\"top\" id=\"bodyCell\" style=\"-webkit-text-size-adjust: 100%;-ms-text-size-adjust: 100%;mso-table-lspace: 0pt;mso-table-rspace: 0pt;margin: 0;padding: 20px;font-family: &quot;ProximaNova&quot;, sans-serif;height: 100% !important;\">\r\n      <div class=\"main\">\r\n        <p style=\"text-align: center;-webkit-text-size-adjust: 100%;-ms-text-size-adjust: 100%; margin-bottom: 30px;\">\r\n          <img src=\"https://www.augmentcode.com/android-chrome-512x512.png\" width=\"50\" alt=\"Your logo goes here\" style=\"-ms-interpolation-mode: bicubic;border: 0;height: auto;line-height: 100%;outline: none;text-decoration: none;\">\r\n        </p>\r\n\r\n        <!-- Email change content -->\r\n        \r\n\r\n          <!-- Signup email content -->\r\n          \r\n\r\n            <p style=\"font-size: 1.4em; line-height: 1.3;\">Your verification code is: <b>811700</b></p>\r\n\r\n          \r\n\r\n        \r\n\r\n        <p style=\"-webkit-text-size-adjust: 100%;-ms-text-size-adjust: 100%;\">If you are having any issues with your account, please don't hesitate to contact us by replying to this mail.</p>\r\n\r\n        <br>\r\n        Thanks!\r\n        <br>\r\n\r\n        <strong>Augment Code</strong>\r\n\r\n        <br><br>\r\n        <hr style=\"border: 2px solid #EAEEF3; border-bottom: 0; margin: 20px 0;\">\r\n        <p style=\"text-align: center;color: #A9B3BC;-webkit-text-size-adjust: 100%;-ms-text-size-adjust: 100%;\">\r\n          If you did not make this request, you can safely ignore this email. Never share this one-time code with anyone - Augment support will never ask for your verification code. Your account remains secure and no action is needed.\r\n        </p>\r\n      </div>\r\n      </td>\r\n    </tr>\r\n  </table>\r\n</center>\r\n</body>\r\n</html>",
  "bodyText": "\r\n\r\n  \r\n  \r\n  \r\n    \r\n      \r\n      \r\n        \r\n          \r\n        \r\n\r\n        \r\n        \r\n\r\n          \r\n          \r\n\r\n            Your verification code is: 811700\r\n\r\n          \r\n\r\n        \r\n\r\n        If you are having any issues with your account, please don't hesitate to contact us by replying to this mail.\r\n\r\n        \r\n        Thanks!\r\n        \r\n\r\n        Augment Code\r\n\r\n        \r\n        \r\n        \r\n          If you did not make this request, you can safely ignore this email. Never share this one-time code with anyone - Augment support will never ask for your verification code. Your account remains secure and no action is needed.\r\n        \r\n      \r\n      \r\n    \r\n  \r\n\r\n\r\n",
  "attachments": [],
  "bigAttachments": [],
  "mailidEncode": "D23F7754F847FDC9F286EB8B917D2D134FFF3FF321B371DEB16CD43CCD987588E968A7620F767A74AF22FEAE653718A4",
  "sendDate": "*************",
  "isStar": false,
  "pReturn": null,
  "tags": [],
  "unRead": false,
  "pNLink": null,
  "sourceID": "b8c7672a0b6d472b893ac26be5ac5c6b",
  "isReturn": "",
  "size": 7219,
  "sessionFrom": "<EMAIL>",
  "sender": "",
  "isDuoyi": false,
  "emailFlag": 0
}
2025-07-08 08:15:41 - __main__ - INFO - ================================================================================
2025-07-08 08:15:41 - __main__ - INFO - 📧 保留邮件用于调试
2025-07-08 08:15:41 - __main__ - INFO - 🔍 尝试从文本内容提取验证码...
2025-07-08 08:15:41 - __main__ - INFO - 🔍 开始提取验证码...
2025-07-08 08:15:41 - __main__ - INFO - 📝 邮件文本长度: 685
2025-07-08 08:15:41 - __main__ - INFO - 📝 邮件文本内容预览:
2025-07-08 08:15:41 - __main__ - INFO - ------------------------------------------------------------
2025-07-08 08:15:41 - __main__ - INFO - 



  

  

  

    

      

      

        

          

        



        

        



          

          



            Your verification code is: 811700



          



        



        If you are having any issues with your account, please don't hesitate to contact us by replying to this mail.



        

        Thanks!

        



        Augment Code



        

        

        

          If you did not make this request, you can safely ignore this email. Never share t...
2025-07-08 08:15:41 - __main__ - INFO - ------------------------------------------------------------
2025-07-08 08:15:41 - __main__ - INFO - 🔍 尝试模式 1: 验证码关键词匹配
2025-07-08 08:15:41 - __main__ - INFO -    正则表达式: (?:verification code|验证码)[^\d]*?(\d{4,8})
2025-07-08 08:15:41 - __main__ - INFO - ✅ 模式 1 匹配成功!
2025-07-08 08:15:41 - __main__ - INFO -    提取到验证码: 811700
2025-07-08 08:15:41 - __main__ - INFO -    匹配的完整文本: verification code is: 811700
2025-07-08 08:15:41 - __main__ - INFO - ✅ 成功提取验证码: 811700
2025-07-08 08:15:41 - __main__ - INFO - 成功获取验证码: 811700
2025-07-08 08:15:41 - __main__ - INFO - ✅ API调用 - 成功获取验证码: <EMAIL> -> 811700
2025-07-08 08:15:41 - werkzeug - INFO - 127.0.0.1 - - [08/Jul/2025 08:15:41] "GET /get_code?email=<EMAIL> HTTP/1.1" 200 -
2025-07-08 08:21:00 - __main__ - INFO - ==================================================
2025-07-08 08:21:00 - __main__ - INFO - 2925邮箱服务端启动 - Windows版本
2025-07-08 08:21:00 - __main__ - INFO - ==================================================
2025-07-08 08:21:00 - __main__ - INFO - 服务端口: 8080
2025-07-08 08:21:00 - __main__ - INFO - 邮箱前缀: codegmail
2025-07-08 08:21:00 - __main__ - INFO - 邮箱域名: 2925.com
2025-07-08 08:21:00 - __main__ - INFO - 日志目录: D:\PythonProject\服务验证端\logs
2025-07-08 08:21:00 - __main__ - INFO - 日志轮转: 10MB/文件, 保留5个备份
2025-07-08 08:21:00 - __main__ - INFO - 启动时间: 2025-07-08 08:21:00
2025-07-08 08:21:00 - __main__ - INFO - Python版本: 3.13.5 (tags/v3.13.5:6cb20a2, Jun 11 2025, 16:15:46) [MSC v.1943 64 bit (AMD64)]
2025-07-08 08:21:00 - __main__ - INFO - 操作系统: Windows
2025-07-08 08:21:00 - __main__ - INFO - ==================================================
2025-07-08 08:21:00 - __main__ - INFO - 正在测试2925邮箱系统连接...
2025-07-08 08:21:00 - __main__ - INFO - 正在登录邮箱前缀: codegmail
2025-07-08 08:21:00 - __main__ - INFO - 正在登录邮箱前缀: codegmail
2025-07-08 08:21:01 - __main__ - INFO - 登录成功: codegmail
2025-07-08 08:21:01 - __main__ - INFO - 登录成功: codegmail
2025-07-08 08:21:01 - __main__ - INFO - ✅ 2925邮箱系统登录成功
2025-07-08 08:21:01 - __main__ - INFO - ✅ 邮箱前缀 'codegmail' 连接正常
2025-07-08 08:21:01 - __main__ - WARNING - ⚠️ 获取验证码失败: 未指定目标邮箱
2025-07-08 08:21:01 - __main__ - INFO - ==================================================
2025-07-08 08:21:01 - __main__ - WARNING - ⚠️  服务启动完成，但2925邮箱系统连接异常
2025-07-08 08:21:01 - __main__ - WARNING - ⚠️  API调用时会自动重试连接
2025-07-08 08:21:01 - __main__ - INFO - 🌐 服务地址: http://0.0.0.0:8080
2025-07-08 08:21:01 - __main__ - INFO - 📋 可用接口: /, /health, /get_email, /get_code, /get_domains
2025-07-08 08:21:01 - __main__ - INFO - ==================================================
2025-07-08 08:21:01 - __main__ - WARNING - 未安装waitress，使用Flask开发服务器运行
2025-07-08 08:21:01 - __main__ - WARNING - 建议安装waitress: pip install waitress
2025-07-08 08:21:01 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:8080
 * Running on http://*************:8080
2025-07-08 08:21:01 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-08 08:22:23 - __main__ - INFO - � API调用 - 单次获取验证码: <EMAIL>
2025-07-08 08:22:23 - __main__ - INFO - 单次获取邮箱验证码: <EMAIL>
2025-07-08 08:22:23 - __main__ - INFO - 创建新的登录实例: codegmail
2025-07-08 08:22:23 - __main__ - INFO - 正在连接2925邮箱系统 (前缀: codegmail)...
2025-07-08 08:22:23 - __main__ - INFO - 正在登录邮箱前缀: codegmail
2025-07-08 08:22:23 - __main__ - INFO - 登录成功: codegmail
2025-07-08 08:22:23 - __main__ - INFO - ✅ 2925邮箱登录成功: <EMAIL>
2025-07-08 08:22:24 - __main__ - INFO - 📧 处理邮件: No Subject | ID: 0ca0fa9edea74bc498439e329b338227
2025-07-08 08:22:24 - __main__ - INFO - �️ 邮件已删除
2025-07-08 08:22:24 - __main__ - INFO - ✅ 验证码提取成功: 811700 (使用模式: 验证码关键词匹配)
2025-07-08 08:22:24 - __main__ - INFO - 成功获取验证码: 811700
2025-07-08 08:22:24 - __main__ - INFO - ✅ API调用 - 成功获取验证码: <EMAIL> -> 811700
2025-07-08 08:22:24 - werkzeug - INFO - 127.0.0.1 - - [08/Jul/2025 08:22:24] "GET /get_code?email=<EMAIL> HTTP/1.1" 200 -
